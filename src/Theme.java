public class Theme {
  private static boolean isDark = false;
  private static int primaryColor = 0x8F4C38;
  private static int onPrimaryColor = 0xFFFFFF;

  private static int backgroundColor = 0xFFF8F6;
  private static int onBackgroundColor = 0x231917;

  public static boolean isDark() {
    return isDark;
  }

  public static void setDark(boolean isDark) {
    Theme.isDark = isDark;
    if (isDark) {
      setPrimaryColor(0xFFB5A0);
      setOnPrimaryColor(0x561F0F);
      setBackgroundColor(0x1A110F);
      setOnBackgroundColor(0xF1DFDA);
    } else {
      setPrimaryColor(0x8F4C38);
      setOnPrimaryColor(0xFFFFFF);
      setBackgroundColor(0xFFF8F6);
      setOnBackgroundColor(0x231917);
    }
  }

  public static int getOnPrimaryColor() {
    return onPrimaryColor;
  }

  public static void setOnPrimaryColor(int onPrimaryColor) {
    Theme.onPrimaryColor = onPrimaryColor;
  }

  public static int getOnBackgroundColor() {
    return onBackgroundColor;
  }

  public static void setOnBackgroundColor(int onBackgroundColor) {
    Theme.onBackgroundColor = onBackgroundColor;
  }

  public static int getBackgroundColor() {
    return backgroundColor;
  }

  public static void setBackgroundColor(int backgroundColor) {
    Theme.backgroundColor = backgroundColor;
  }

  public static int getPrimaryColor() {
    return primaryColor;
  }

  public static void setPrimaryColor(int primaryColor) {
    Theme.primaryColor = primaryColor;
  }
}
