public class Theme {
  private static boolean isDark = false;
  private static int primaryColor = 0x8F4C38;
  private static int onPrimaryColor = 0xFFFFFF;

  private static int backgroundColor = 0xFFF8F6;
  private static int onBackgroundColor = 0x231917;

  private static int surfaceColor = 0xFFF8F6;
  private static int onSurfaceColor = 0x231917;
  private static int surfaceVariantColor = 0xF5DED8;
  private static int onSurfaceVariantColor = 0x53433F;
  private static int outlineColor = 0x85736E;
  private static int outlineVariantColor = 0xD8C2BC;

  public static boolean isDark() {
    return isDark;
  }

  public static void setDark(boolean isDark) {
    Theme.isDark = isDark;
    if (isDark) {
      setPrimaryColor(0xFFB5A0);
      setOnPrimaryColor(0x561F0F);
      setBackgroundColor(0x1A110F);
      setOnBackgroundColor(0xF1DFDA);
      setSurfaceColor(0x1A110F);
      setOnSurfaceColor(0xF1DFDA);
      setSurfaceVariantColor(0x53433F);
      setOnSurfaceVariantColor(0xD8C2BC);
      setOutlineColor(0xA08C87);
      setOutlineVariantColor(0x53433F);
    } else {
      setPrimaryColor(0x8F4C38);
      setOnPrimaryColor(0xFFFFFF);
      setBackgroundColor(0xFFF8F6);
      setOnBackgroundColor(0x231917);
      setSurfaceColor(0xFFF8F6);
      setOnSurfaceColor(0x231917);
      setSurfaceVariantColor(0xF5DED8);
      setOnSurfaceVariantColor(0x53433F);
      setOutlineColor(0x85736E);
      setOutlineVariantColor(0xD8C2BC);
    }
  }

  public static int getOnPrimaryColor() {
    return onPrimaryColor;
  }

  public static void setOnPrimaryColor(int onPrimaryColor) {
    Theme.onPrimaryColor = onPrimaryColor;
  }

  public static int getOnBackgroundColor() {
    return onBackgroundColor;
  }

  public static void setOnBackgroundColor(int onBackgroundColor) {
    Theme.onBackgroundColor = onBackgroundColor;
  }

  public static int getBackgroundColor() {
    return backgroundColor;
  }

  public static void setBackgroundColor(int backgroundColor) {
    Theme.backgroundColor = backgroundColor;
  }

  public static int getPrimaryColor() {
    return primaryColor;
  }

  public static void setPrimaryColor(int primaryColor) {
    Theme.primaryColor = primaryColor;
  }

  public static int getSurfaceColor() {
    return surfaceColor;
  }

  public static void setSurfaceColor(int surfaceColor) {
    Theme.surfaceColor = surfaceColor;
  }

  public static int getOnSurfaceColor() {
    return onSurfaceColor;
  }

  public static void setOnSurfaceColor(int onSurfaceColor) {
    Theme.onSurfaceColor = onSurfaceColor;
  }

  public static int getSurfaceVariantColor() {
    return surfaceVariantColor;
  }

  public static void setSurfaceVariantColor(int surfaceVariantColor) {
    Theme.surfaceVariantColor = surfaceVariantColor;
  }

  public static int getOnSurfaceVariantColor() {
    return onSurfaceVariantColor;
  }

  public static void setOnSurfaceVariantColor(int onSurfaceVariantColor) {
    Theme.onSurfaceVariantColor = onSurfaceVariantColor;
  }

  public static int getOutlineColor() {
    return outlineColor;
  }

  public static void setOutlineColor(int outlineColor) {
    Theme.outlineColor = outlineColor;
  }

  public static int getOutlineVariantColor() {
    return outlineVariantColor;
  }

  public static void setOutlineVariantColor(int outlineVariantColor) {
    Theme.outlineVariantColor = outlineVariantColor;
  }
}
